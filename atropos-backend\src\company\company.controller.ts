// src/company/company.controller.ts
import { Controller, Get, Post, Body, Patch, Param, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { CompanyService } from './company.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';

@Controller('company')
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Post() // POST /company
  @HttpCode(HttpStatus.CREATED) // Başarılı olursa 201 Created döndür
  create(@Body() createCompanyDto: CreateCompanyDto) {
    return this.companyService.createCompany(createCompanyDto);
  }

  @Get() // GET /company (Zaten vardı, üzerine yazdık)
  findAll() {
    return this.companyService.findAllCompanies();
  }

  @Get(':id') // GET /company/:id
  findOne(@Param('id') id: string) {
    return this.companyService.findOneCompany(id);
  }

  @Patch(':id') // PATCH /company/:id
  update(@Param('id') id: string, @Body() updateCompanyDto: UpdateCompanyDto) {
    return this.companyService.updateCompany(id, updateCompanyDto);
  }

  @Delete(':id') // DELETE /company/:id
  @HttpCode(HttpStatus.NO_CONTENT) // Başarılı silme için 204 No Content döndür
  remove(@Param('id') id: string) {
    return this.companyService.removeCompany(id);
  }
}