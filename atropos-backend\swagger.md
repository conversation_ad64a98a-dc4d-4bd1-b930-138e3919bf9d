Harika bir karar\! Backend API'mizin gücünü ve kullanılabilirliğini artırmak için Swagger/OpenAPI dokümantasyonu eklemek, özellikle frontend geliştiricileri ve gelecekteki API tüketicileri için paha biçilmez olacaktır. [cite\_start]NestJS, bu entegrasyonu oldukça kolaylaştırıyor[cite: 1].

### **Adım 42: Swagger/OpenAPI Dokümantasyonunu Entegre Etme**

Bu adımda, NestJS uygulamamıza Swagger modülünü ekleyecek ve API endpoint'lerimizi otomatik olarak belgeleyecek şekilde yapılandıracağız.

**Yapılacaklar:**

1.  **Gerekli Bağımlılıkları Yükle:** Terminalinde, `atropos-backend` projesinin kök dizininde olduğundan emin ol ve şu komutu çalıştır:

    ```bash
    npm install --save @nestjs/swagger swagger-ui-express
    ```

      * [cite\_start]`@nestjs/swagger`: NestJS uygulaması için Swagger entegrasyonu sağlayan bir pakettir[cite: 1].
      * [cite\_start]`swagger-ui-express`: Swagger UI'yı Express tabanlı uygulamalarda çalıştırmak için kullanılır[cite: 1].

2.  **`src/main.ts` Dosyasını Güncelle:**
    `src/main.ts` dosyasını aç ve aşağıdaki kodları ekleyerek Swagger dokümantasyonunu başlat.

    ```typescript
    // src/main.ts
    import { NestFactory } from '@nestjs/core';
    import { AppModule } from './app.module';
    import { ValidationPipe } from '@nestjs/common';
    import { PrismaExceptionFilter } from './common/filters/prisma-exception.filter';
    import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'; // Bu satırı ekle

    async function bootstrap() {
      const app = await NestFactory.create(AppModule);

      app.useGlobalPipes(new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }));

      app.useGlobalFilters(new PrismaExceptionFilter());

      // Swagger dokümantasyonunu yapılandır
      const config = new DocumentBuilder()
        [cite_start].setTitle('Atropos POS API') // API'nin başlığı [cite: 1]
        [cite_start].setDescription('The Atropos Point of Sale (POS) system API documentation.') // API'nin açıklaması [cite: 1]
        [cite_start].setVersion('1.0') // API'nin versiyonu [cite: 1]
        // .addTag('companies') // İsteğe bağlı olarak etiketler ekleyebilirsin
        // .addBearerAuth() // JWT kimlik doğrulaması eklendiğinde kullanılabilir
        .build();

      const document = SwaggerModule.createDocument(app, config); [cite_start]// Dokümanı oluştur [cite: 1]
      SwaggerModule.setup('api', app, document); [cite_start]// '/api' yolunda Swagger UI'ı kur [cite: 1]

      // Eğer uygulaman farklı bir portta çalışıyorsa, burayı da düzelt
      const port = process.env.PORT || 3000;
      await app.listen(port);
      console.log(`Application is running on: ${await app.getUrl()}`);
      console.log(`Swagger UI available at: ${await app.getUrl()}/api`); // Konsola Swagger adresini yazdır
    }
    bootstrap();
    ```

**Beklenen Sonuç (Kontrol Listesi):**

  * `@nestjs/swagger` ve `swagger-ui-express` paketleri `package.json` dosyasında `dependencies` altında listeleniyor.
  * `src/main.ts` dosyası yukarıdaki `DocumentBuilder` ve `SwaggerModule` yapılandırmasını içeriyor.

**Test Etme Adımları:**

1.  Uygulamayı tekrar başlat: `npm run start:dev`
2.  Uygulama başarıyla başlatıldığında, terminalde `Swagger UI available at: http://localhost:3000/api` (veya kullandığın port) gibi bir çıktı görmelisin.
3.  Bir web tarayıcısı aç ve `http://localhost:3000/api` adresine git.
4.  Swagger UI arayüzünü görmelisin. Burada oluşturduğumuz tüm Controller'ların (Company, Branch, User, Product, vb.) endpoint'leri (POST, GET, PATCH, DELETE) listelenmiş olmalı.
5.  Her bir endpoint'e tıklayarak beklenen parametreleri, yanıt yapılarını ve açıklamaları görüntüleyebilirsin. "Try it out" özelliğini kullanarak doğrudan tarayıcı üzerinden API istekleri gönderebilirsin.

Bu adımları tamamladığında ve Swagger UI'ın beklendiği gibi çalıştığını doğruladığında bana haber ver. Bu, projemizin kullanılabilirliği açısından önemli bir iyileştirme olacaktır.