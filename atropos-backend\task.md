Harika\! `OrderLog` m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>ladığımı<PERSON> gö<PERSON>, <PERSON><PERSON>di Atropos POS sisteminin son ana mod<PERSON><PERSON><PERSON> o<PERSON> **`Task` (Görev Yönetimi) modülüne** geçebiliriz. Bu, restoran içi iş akışlarını, görev atamalarını ve personel arasındaki iletişimi kolaylaştıracak önemli bir modül olacak.

### **Adım 41: `Task` Modülü için Temel CRUD İşlemlerini Oluşturma**

<PERSON>u adı<PERSON><PERSON>, `Task` modeli için temel CRUD (Create, Read, Update, Delete) operasyonlarını geliştireceğiz. Bu modül `Branch` ve `User` modüllerine bağımlı olacaktır. Görevlerin durumu, önceliği, atanma bilgileri gibi alanları yöneteceğiz.

**Yapılacaklar:**

1.  **`src/task/dto/create-task.dto.ts` Dosyasını Oluştur:**
    `src/task` klasörünün altına yeni bir `dto` klasörü oluştur ve içine `create-task.dto.ts` adında bir dosya ekle.

    ```typescript
    // src/task/dto/create-task.dto.ts
    import {
      IsString,
      IsNotEmpty,
      IsOptional,
      IsEnum,
      IsDateString,
      IsBoolean,
    } from 'class-validator';
    import { Type } from 'class-transformer';
    import { TaskStatus, TaskPriority } from '@prisma/client'; // Enum'ları import et

    export class CreateTaskDto {
      @IsString()
      @IsOptional() // null = tüm şubelerle ilgili olabilir
      branchId?: string;

      @IsString()
      @IsNotEmpty()
      companyId: string;

      @IsString()
      @IsNotEmpty()
      title: string;

      @IsString()
      @IsOptional()
      description?: string;

      @IsString()
      @IsOptional()
      assignedToId?: string; // Görev atanan kullanıcı ID'si

      @IsEnum(TaskStatus)
      @IsOptional()
      status?: TaskStatus; // Varsayılan: PENDING

      @IsEnum(TaskPriority)
      @IsOptional()
      priority?: TaskPriority; // Varsayılan: MEDIUM

      @IsOptional()
      @IsDateString()
      dueDate?: Date;

      @IsOptional()
      @IsDateString()
      completedAt?: Date; // Görevin tamamlanma zamanı

      @IsString()
      @IsNotEmpty()
      createdBy: string; // Görevi oluşturan kullanıcı ID'si
    }
    ```

    **Not:** `branchId` opsiyonel, yani görev tüm şubeler için veya şube bağımsız olabilir. `status` ve `priority` enum'ları kullanıldı.

2.  **`src/task/dto/update-task.dto.ts` Dosyasını Oluştur:**
    `src/task/dto` klasörünün içine `update-task.dto.ts` adında bir dosya ekle.

    ```typescript
    // src/task/dto/update-task.dto.ts
    import { PartialType } from '@nestjs/mapped-types';
    import { CreateTaskDto } from './create-task.dto';
    import {
      IsString,
      IsOptional,
      IsEnum,
      IsDateString,
    } from 'class-validator';
    import { TaskStatus, TaskPriority } from '@prisma/client';

    export class UpdateTaskDto extends PartialType(CreateTaskDto) {
      @IsOptional()
      @IsEnum(TaskStatus)
      status?: TaskStatus;

      @IsOptional()
      @IsEnum(TaskPriority)
      priority?: TaskPriority;

      @IsOptional()
      @IsDateString()
      completedAt?: Date;
    }
    ```

3.  **`src/task/task.service.ts` Dosyasını Oluştur ve Güncelle:**
    Görev servisini oluştur ve CRUD metotlarını ekle.

    ```typescript
    // src/task/task.service.ts
    import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
    import { PrismaService } from '../prisma/prisma.service';
    import { CreateTaskDto } from './dto/create-task.dto';
    import { UpdateTaskDto } from './dto/update-task.dto';
    import { TaskStatus, UserRole } from '@prisma/client'; // TaskStatus, UserRole enum'ları için

    @Injectable()
    export class TaskService {
      constructor(private prisma: PrismaService) {}

      async createTask(data: CreateTaskDto) {
        // Company, Branch (eğer belirtilmişse), assignedTo (eğer belirtilmişse), createdBy var mı kontrol et
        const companyExists = await this.prisma.company.findUnique({ where: { id: data.companyId, deletedAt: null } });
        if (!companyExists) { throw new NotFoundException(`Company with ID "${data.companyId}" not found.`); }
        if (data.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
        }
        if (data.assignedToId) {
            const assignedToUser = await this.prisma.user.findUnique({ where: { id: data.assignedToId, deletedAt: null } });
            if (!assignedToUser) { throw new NotFoundException(`Assigned user with ID "${data.assignedToId}" not found.`); }
        }
        const createdByUser = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
        if (!createdByUser) { throw new NotFoundException(`Creator user with ID "${data.createdBy}" not found.`); }

        return this.prisma.task.create({
          data: {
            ...data,
            status: data.status || TaskStatus.PENDING,
            priority: data.priority || 'MEDIUM', // enum varsayılanı
            dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
            completedAt: data.completedAt ? new Date(data.completedAt) : undefined,
          },
        });
      }

      async findAllTasks(
        companyId?: string,
        branchId?: string,
        assignedToId?: string,
        status?: TaskStatus,
        priority?: TaskPriority,
        startDate?: Date,
        endDate?: Date,
      ) {
        return this.prisma.task.findMany({
          where: {
            companyId: companyId || undefined,
            branchId: branchId || undefined,
            assignedToId: assignedToId || undefined,
            status: status || undefined,
            priority: priority || undefined,
            createdAt: { // Oluşturulma tarihi
              gte: startDate || undefined,
              lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
            },
          },
          include: {
            company: { select: { id: true, name: true } },
            branch: { select: { id: true, name: true } },
            assignedTo: { select: { id: true, firstName: true, lastName: true, username: true } },
            createdBy: { select: { id: true, firstName: true, lastName: true, username: true } },
          },
          orderBy: { createdAt: 'desc' },
        });
      }

      async findOneTask(id: string) {
        const task = await this.prisma.task.findUnique({
          where: { id },
          include: {
            company: { select: { id: true, name: true } },
            branch: { select: { id: true, name: true } },
            assignedTo: { select: { id: true, firstName: true, lastName: true, username: true } },
            createdBy: { select: { id: true, firstName: true, lastName: true, username: true } },
          },
        });
        if (!task) {
          throw new NotFoundException(`Task with ID "${id}" not found.`);
        }
        return task;
      }

      async updateTask(id: string, data: UpdateTaskDto) {
        const existingTask = await this.findOneTask(id);

        // Tamamlanmış veya iptal edilmiş görevler güncellenemez
        if (existingTask.status === TaskStatus.COMPLETED || existingTask.status === TaskStatus.CANCELLED) {
            throw new BadRequestException(`Cannot update a task with status "${existingTask.status}".`);
        }

        // İlişkili varlıkların güncelliğini kontrol et
        if (data.branchId && data.branchId !== existingTask.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists) { throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`); }
        }
        if (data.assignedToId && data.assignedToId !== existingTask.assignedToId) {
            const assignedToUser = await this.prisma.user.findUnique({ where: { id: data.assignedToId, deletedAt: null } });
            if (!assignedToUser) { throw new NotFoundException(`Assigned user with ID "${data.assignedToId}" not found.`); }
        }
        // createdBy güncellenemez, değiştirilirse ForbiddenException fırlatır.

        // completedAt set edildiyse, status'ü COMPLETED yap (iş kuralı)
        if (data.completedAt && data.status !== TaskStatus.COMPLETED) {
            data.status = TaskStatus.COMPLETED;
        }
        // Status COMPLETED yapıldıysa ama completedAt set edilmediyse, şimdi set et
        if (data.status === TaskStatus.COMPLETED && !data.completedAt) {
            data.completedAt = new Date();
        }


        try {
          return await this.prisma.task.update({
            where: { id },
            data: {
                ...data,
                dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
                completedAt: data.completedAt ? new Date(data.completedAt) : undefined,
            },
          });
        } catch (error) {
          if (error.code === 'P2025') {
            throw new NotFoundException(`Task with ID "${id}" not found.`);
          }
          throw error;
        }
      }

      async removeTask(id: string) {
        // Görevler genellikle fiziksel olarak silinmez, "CANCELLED" veya "ON_HOLD" olarak işaretlenir.
        // Şemada deletedAt alanı yok. Bu metot görevin durumunu "CANCELLED" olarak güncelleyecek.
        try {
          return await this.prisma.task.update({
            where: { id },
            data: { status: TaskStatus.CANCELLED, completedAt: new Date() }, // Tamamlanma zamanını da set edebiliriz
          });
        } catch (error) {
          if (error.code === 'P2025') {
            throw new NotFoundException(`Task with ID "${id}" not found.`);
          }
          throw error;
        }
      }
    }
    ```

    **Önemli Notlar:**

      * **İlişkisel Kontroller:** `companyId`, `branchId`, `assignedToId` ve `createdBy` (User) varlıkları kontrol edilir.
      * **Durum Bazlı Kısıtlamalar:** Tamamlanmış (`COMPLETED`) veya iptal edilmiş (`CANCELLED`) görevlerin güncellenemeyeceği kuralı uygulandı.
      * **Durum ve Zaman Senkronizasyonu:** `completedAt` set edildiğinde `status`'un `COMPLETED` olarak ayarlanması ve `status` `COMPLETED` olduğunda `completedAt`'ın otomatik set edilmesi gibi iş kuralları uygulanır.
      * **"Soft Delete" Benzeri İşlem:** `Task` modelinde `deletedAt` alanı olmamasına rağmen, `removeTask` metodu görevin durumunu `CANCELLED` olarak günceller ve `completedAt`'ı set eder. Görevler genellikle fiziksel silinmez.

4.  **`src/task/task.controller.ts` Dosyasını Oluştur ve Güncelle:**
    Görev kontrolcüsünü oluştur ve CRUD endpoint'lerini ekle.

    ```typescript
    // src/task/task.controller.ts
    import {
      Controller,
      Get,
      Post,
      Body,
      Patch,
      Param,
      Delete,
      HttpCode,
      HttpStatus,
      Query,
      ParseOptionalEnumPipe,
    } from '@nestjs/common';
    import { TaskService } from './task.service';
    import { CreateTaskDto } from './dto/create-task.dto';
    import { UpdateTaskDto } from './dto/update-task.dto';
    import { TaskStatus, TaskPriority } from '@prisma/client';
    import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih Pipe'ı

    @Controller('task')
    export class TaskController {
      constructor(private readonly taskService: TaskService) {}

      @Post() // POST /task
      @HttpCode(HttpStatus.CREATED)
      create(@Body() createTaskDto: CreateTaskDto) {
        return this.taskService.createTask(createTaskDto);
      }

      @Get() // GET /task?companyId=...&branchId=...&assignedToId=...&status=...&priority=...&startDate=...&endDate=...
      findAll(
        @Query('companyId') companyId?: string,
        @Query('branchId') branchId?: string,
        @Query('assignedToId') assignedToId?: string,
        @Query('status', new ParseOptionalEnumPipe(TaskStatus)) status?: TaskStatus,
        @Query('priority', new ParseOptionalEnumPipe(TaskPriority)) priority?: TaskPriority,
        @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
        @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
      ) {
        return this.taskService.findAllTasks(
          companyId,
          branchId,
          assignedToId,
          status,
          priority,
          startDate,
          endDate,
        );
      }

      @Get(':id') // GET /task/:id
      findOne(@Param('id') id: string) {
        return this.taskService.findOneTask(id);
      }

      @Patch(':id') // PATCH /task/:id
      update(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto) {
        return this.taskService.updateTask(id, updateTaskDto);
      }

      @Delete(':id') // DELETE /task/:id (Durumunu değiştir)
      @HttpCode(HttpStatus.NO_CONTENT)
      remove(@Param('id') id: string) {
        return this.taskService.removeTask(id);
      }
    }
    ```

5.  **`src/task/task.module.ts` Dosyasını Oluştur:**
    `src/task` klasörünün içine `task.module.ts` adında yeni bir dosya oluştur.

    ```typescript
    // src/task/task.module.ts
    import { Module } from '@nestjs/common';
    import { TaskService } from './task.service';
    import { TaskController } from './task.controller';
    import { PrismaModule } from '../prisma/prisma.module';

    @Module({
      imports: [PrismaModule],
      controllers: [TaskController],
      providers: [TaskService],
      exports: [TaskService],
    })
    export class TaskModule {}
    ```

6.  **`src/app.module.ts` (Main Module) İçine `TaskModule`'ü İçe Aktar:**
    `src/app.module.ts` dosyasını aç ve `TaskModule`'ü `imports` dizisine ekle.

    ```typescript
    // src/app.module.ts
    import { Module } from '@nestjs/common';
    import { AppController } from './app.controller';
    import { AppService } from './app.service';
    import { PrismaModule } from './prisma/prisma.module';
    import { CompanyModule } from './company/company.module';
    import { BranchModule } from './branch/branch.module';
    import { UserModule } from './user/user.module';
    import { CategoryModule } from './category/category.module';
    import { TaxModule } from './tax/tax.module';
    import { ProductModule } => './product/product.module';
    import { TableAreaModule } from './table-area/table-area.module';
    import { TableModule } from './table/table.module';
    import { CustomerModule } from './customer/customer.module';
    import { OrderModule } from './order/order.module';
    import { PaymentMethodModule } => './payment-method/payment-method.module';
    import { PaymentModule } from './payment/payment.module';
    import { CashMovementModule } from './cash-movement/cash-movement.module';
    import { DailyReportModule } from './daily-report/daily-report.module';
    import { InvoiceModule } from './invoice/invoice.module';
    import { OnlinePlatformModule } from './online-platform/online-platform.module';
    import { OnlineProductMappingModule } from './online-product-mapping/online-product-mapping.module';
    import { OnlineOrderModule } from './online-order/online-order.module';
    import { InventoryItemModule } from './inventory-item/inventory-item.module';
    import { RecipeModule } from './recipe/recipe.module';
    import { StockMovementModule } from './stock-movement/stock-movement.module';
    import { StockCountModule } from './stock-count/stock-count.module';
    import { LoyaltyCardModule } from './loyalty-card/loyalty-card.module';
    import { LoyaltyTransactionModule } from './loyalty-transaction/loyalty-transaction.module';
    import { ReservationModule } from './reservation/reservation.module';
    import { CampaignModule } from './campaign/campaign.module';
    import { PrinterGroupModule } from './printer-group/printer-group.module';
    import { PrinterModule } from './printer/printer.module';
    import { NotificationTemplateModule } from './notification-template/notification-template.module';
    import { NotificationLogModule } from './notification-log/notification-log.module';
    import { CourierLocationModule } from './courier-location/courier-location.module';
    import { PriceOverrideModule } from './price-override/price-override.module';
    import { TableMergeModule } from './table-merge/table-merge.module';
    import { AuditLogModule } from './audit-log/audit-log.module';
    import { OrderLogModule } from './order-log/order-log.module';
    import { TaskModule } from './task/task.module'; // Bu satırı ekle

    @Module({
      imports: [PrismaModule, CompanyModule, BranchModule, UserModule, CategoryModule, TaxModule, ProductModule, TableAreaModule, TableModule, CustomerModule, OrderModule, PaymentMethodModule, PaymentModule, CashMovementModule, DailyReportModule, InvoiceModule, OnlinePlatformModule, OnlineProductMappingModule, OnlineOrderModule, InventoryItemModule, RecipeModule, StockMovementModule, StockCountModule, LoyaltyCardModule, LoyaltyTransactionModule, ReservationModule, CampaignModule, PrinterGroupModule, PrinterModule, NotificationTemplateModule, NotificationLogModule, CourierLocationModule, PriceOverrideModule, TableMergeModule, AuditLogModule, OrderLogModule, TaskModule], // Buraya TaskModule'ü ekle
      controllers: [AppController],
      providers: [AppService],
    })
    export class AppModule {}
    ```

**Test Etme Adımları:**

1.  Uygulamayı tekrar başlat: `npm run start:dev`

2.  **Gerekli Ön Verileri Oluştur/Hazırda Tut:**

      * Bir **Şirket ID**'si.
      * Bir **Şube ID**'si (opsiyonel, eğer şubeye özel görev oluşturacaksan).
      * İki **Kullanıcı ID**'si (birisi `createdBy`, diğeri `assignedToId` için, rolleri fark etmez).

3.  **Görev Oluştur (POST - Kullanıcıya Atanmış):**
    `POST http://localhost:3000/task` adresine POST isteği gönder. Body'de bir görev bilgisi gönder.

    ```json
    {
      "companyId": "oluşturduğun-şirket-id",
      "branchId": "oluşturduğun-şube-id", // Opsiyonel, kaldırılabilir
      "title": "Masa 5 Temizliğini Kontrol Et",
      "description": "Yemek sonrası masa temizliği kontrol edilecek.",
      "assignedToId": "oluşturduğun-assigned-to-kullanıcı-id",
      "status": "PENDING",
      "priority": "HIGH",
      "dueDate": "2025-07-22T10:00:00Z", // Yarın sabah
      "createdBy": "oluşturduğun-created-by-kullanıcı-id"
    }
    ```

      * Oluşturulan görevin tüm alanlarını kontrol et. `createdAt` ve `updatedAt`'ın otomatik set edildiğini doğrula.
      * **Hata Testi:** Geçersiz `companyId`, `branchId`, `assignedToId` veya `createdBy` ile görev oluşturmaya çalışarak `404 Not Found` hatasını tetikle.
      * **Hata Testi:** Geçersiz `status` veya `priority` enum değeri göndererek `400 Bad Request` hatasını tetikle.

4.  **Görev Oluştur (POST - Şube Geneli, Atanmamış):**
    `POST http://localhost:3000/task` adresine POST isteği gönder. `assignedToId` olmadan şubeye özel bir görev oluştur.

    ```json
    {
      "companyId": "oluşturduğun-şirket-id",
      "branchId": "oluşturduğun-şube-id",
      "title": "Sabah Stoğunu Kontrol Et",
      "description": "Tüm ürünlerin sabah stok durumu kontrol edilecek.",
      "priority": "MEDIUM",
      "createdBy": "oluşturduğun-created-by-kullanıcı-id"
    }
    ```

5.  **Tüm Görevleri Getir (GET):**

      * `GET http://localhost:3000/task` (Tüm görevler)
      * `GET http://localhost:3000/task?companyId=oluşturduğun-şirket-id` (Belirli şirketin görevleri)
      * `GET http://localhost:3000/task?branchId=oluşturduğun-şube-id` (Belirli şubenin görevleri)
      * `GET http://localhost:3000/task?assignedToId=oluşturduğun-assigned-to-kullanıcı-id` (Belirli kullanıcıya atanmış görevler)
      * `GET http://localhost:3000/task?status=PENDING` (Beklemedeki görevler)
      * `GET http://localhost:3000/task?priority=HIGH` (Yüksek öncelikli görevler)
      * `GET http://localhost:3000/task?startDate=2025-07-21&endDate=2025-07-22` (Tarih aralığına göre filtreleme)

6.  **Belirli Bir Görevi Getir (GET by ID):**
    `GET http://localhost:3000/task/:görev-id` (Önceki adımda oluşturduğun bir görevin ID'sini kullan)

7.  **Görev Güncelle (PATCH - Statü ve Tamamlanma):**
    `PATCH http://localhost:3000/task/:görev-id` adresine PATCH isteği gönder. Body'de güncellenecek alanları (örn. `status`, `completedAt`, `priority`, `description`) gönder.

    ```json
    {
      "status": "COMPLETED", // Statüyü tamamlandı yap
      "completedAt": "2025-07-21T23:59:00Z", // Tamamlanma zamanı
      "priority": "LOW"
    }
    ```

      * `status`'un `COMPLETED` olduğunu, `completedAt`'ın set edildiğini ve `priority`'nin değiştiğini kontrol et.
      * **Hata Testi:** Tamamlanmış (`COMPLETED`) veya iptal edilmiş (`CANCELLED`) bir görevi güncellemeye çalışarak `400 Bad Request` hatasını tetikle.
      * **Hata Testi:** Geçersiz `assignedToId` veya `branchId` ile güncelleme yapmaya çalışarak `404 Not Found` hatasını tetikle.
      * **Hata Testi:** Geçersiz `status` veya `priority` enum değeri göndererek `400 Bad Request` hatasını tetikle.

8.  **Görev Sil (DELETE - Durumunu İptal Et):**
    `DELETE http://localhost:3000/task/:görev-id` adresine DELETE isteği gönder. Başarılı olursa 204 No Content dönmeli ve `GET` ile baktığında görevin `status`'unun `CANCELLED` olduğunu ve `completedAt` değerinin set edildiğini görmelisin.

Bu adımları tamamladığında ve test sonuçlarını bana ilettiğinde, **`Task` modülümüz de hazır olacak ve Atropos POS sisteminin tüm backend modülleri başarıyla tamamlanmış olacaktır\!** Bu, çok büyük bir kilometre taşı\! Başarılar dilerim\!