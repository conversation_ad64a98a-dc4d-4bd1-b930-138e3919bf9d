import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, Logger } from '@nestjs/common'; // Logger'ı import et
import { PrismaExceptionFilter } from './common/filters/prisma-exception.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  // Logger'ı burada yapılandırabiliriz
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'verbose', 'debug'], // Hangi seviyelerin loglanacağını belirler
    // Örneğin: logger: process.env.NODE_ENV === 'production' ? ['error', 'warn', 'log'] : ['error', 'warn', 'log', 'verbose', 'debug'],
  });

  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  app.useGlobalFilters(new PrismaExceptionFilter());

  // Swagger dokümantasyonunu yapılandır
  const config = new DocumentBuilder()
    .setTitle('Atropos POS API') // API'nin başlığı
    .setDescription('The Atropos Point of Sale (POS) system API documentation.') // API'nin açıklaması
    .setVersion('1.0') // API'nin versiyonu
    // .addTag('companies') // İsteğe bağlı olarak etiketler ekleyebilirsin
    // .addBearerAuth() // JWT kimlik doğrulaması eklendiğinde kullanılabilir
    .build();

  const document = SwaggerModule.createDocument(app, config); // Dokümanı oluştur
  SwaggerModule.setup('api', app, document); // '/api' yolunda Swagger UI'ı kur

  // Eğer uygulaman farklı bir portta çalışıyorsa, burayı da düzelt
  const port = process.env.PORT || 3000;
  await app.listen(port);
  console.log(`Application is running on: ${await app.getUrl()}`);
  console.log(`Swagger UI available at: ${await app.getUrl()}/api`); // Konsola Swagger adresini yazdır
}
bootstrap();
