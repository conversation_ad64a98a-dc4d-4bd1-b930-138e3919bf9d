// src/company/company.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';

@Injectable()
export class CompanyService {
  constructor(private prisma: PrismaService) {}

  async createCompany(data: CreateCompanyDto) {
    return this.prisma.company.create({ data });
  }

  async findAllCompanies() {
    return this.prisma.company.findMany();
  }

  async findOneCompany(id: string) {
    const company = await this.prisma.company.findUnique({
      where: { id },
    });
    if (!company) {
      throw new NotFoundException(`Company with ID "${id}" not found.`);
    }
    return company;
  }

  async updateCompany(id: string, data: UpdateCompanyDto) {
    try {
      return await this.prisma.company.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') { // Prisma'da kayıt bulunamadı hatası kodu
        throw new NotFoundException(`Company with ID "${id}" not found.`);
      }
      throw error; // Diğer hataları yeniden fırlat
    }
  }

  async removeCompany(id: string) {
    try {
      return await this.prisma.company.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') { // Prisma'da kayıt bulunamadı hatası kodu
        throw new NotFoundException(`Company with ID "${id}" not found.`);
      }
      throw error; // Diğer hataları yeniden fırlat
    }
  }
}